<?php

namespace App\Http\Controllers\Api\v1;

use App\Questionary;
use App\QuestionaryQuestion;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Http\Requests\v1\IndexQuestionaryQuestionRequest;
use App\Http\Requests\v1\StoreQuestionaryQuestionRequest;
use App\Http\Requests\v1\UpdateQuestionaryQuestionRequest;
use App\Http\Requests\v3\Questionary\BulkQuestionaryQuestionRequest;
use App\Traits\SaveFile;
use Exception;

class QuestionaryQuestionController extends Controller
{
    use SaveFile;
    /**
     * Display a listing of the resource.
     *
     * @param  \App\Questionary  $questionary
     * @return \Illuminate\Http\Response
     */
    public function index(Questionary $questionary, IndexQuestionaryQuestionRequest $request)
    {
        $this->authorize('viewAny', [QuestionaryQuestion::class, $questionary]);

        $questionary_questions = $questionary->questions();

        if ($request->has('search')) {
            $questionary_questions = $questionary_questions->whereLike(['question', 'options'], $request->search);
        }

        if ($request->has('orderBy') && $request->has('orderDirection')) {
            $questionary_questions = $questionary_questions->orderBy($request->orderBy, $request->orderDirection);
        } else {
            $questionary_questions = $questionary_questions->latest();
        }

        if ($request->has('page')) {
            return response()->json(collect([
                'message' => 'Questionary Questions returned successfully.',
                'status' => '1'
            ])->merge($questionary_questions->paginate()));
        }

        return response()->json([
            'data' => $questionary_questions->get(),
            'message' => 'Questionary Questions returned successfully.',
            'status' => '1'
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Questionary  $questionary
     * @return \Illuminate\Http\Response
     */
    public function store(StoreQuestionaryQuestionRequest $request, Questionary $questionary)
    {
        $this->authorize('create', [QuestionaryQuestion::class, $questionary]);
        $file = null;
        if (in_array($request->type, config('questionary.type_with_image')) && $request->hasFile('options')) {
            $file = $this->saveFile($request->file('options'), 'questionary_question');
        }

        $questionary_question = $questionary->questions()->create([
            'question' => $request->question,
            'type' => $request->type,
            'required' => $request->boolean('required'),
            'options' => $file ? [$file->filename] : (in_array($request->type, array_keys(config('questionary.type_with_options'))) ? $request->options : null),
            'properties' => $request->properties,
        ]);

        return response()->json([
            'data' => $questionary_question,
            'message' => 'Questionary Question created successfully.',
            'status' => '1'
        ]);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Questionary  $questionary
     * @param  \App\QuestionaryQuestion  $questionaryQuestion
     * @return \Illuminate\Http\Response
     */
    public function show(Questionary $questionary, QuestionaryQuestion $questionaryQuestion)
    {
        $this->authorize('view', [QuestionaryQuestion::class, $questionary, $questionaryQuestion]);

        return response()->json([
            'data' => $questionaryQuestion,
            'message' => 'Questionary Question returned successfully.',
            'status' => '1'
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Questionary  $questionary
     * @param  \App\QuestionaryQuestion  $questionaryQuestion
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateQuestionaryQuestionRequest $request, Questionary $questionary, QuestionaryQuestion $questionaryQuestion)
    {
        $this->authorize('update', [QuestionaryQuestion::class, $questionary, $questionaryQuestion]);

        if ($request->has('question')) {
            $questionaryQuestion->question = $request->question;
        }

        if ($request->has('type')) {
            $questionaryQuestion->type = $request->type;
        }

        if ($request->has('required')) {
            $questionaryQuestion->required = $request->required;
        }

        if ($request->has('properties')) {
            $questionaryQuestion->properties = $request->properties;
        }

        // if ($request->has('order')) {
        //     $questionaryQuestion->order = $request->order;
        // }

        if ($request->has('options')) {
            if (in_array($request->type, config('questionary.type_with_image')) && $request->hasFile('options')) {
                $file = $this->saveFile($request->file('options'), 'questionary_question');
                $questionaryQuestion->options = [$file->filename];
            } else {
                $questionaryQuestion->options = $request->options;
            }
        }

        if ($questionaryQuestion->isDirty()) {
            $questionaryQuestion->save();
        }

        return response()->json([
            'data' => $questionaryQuestion,
            'message' => 'Questionary Question updated successfully.',
            'status' => '1'
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Questionary  $questionary
     * @param  \App\QuestionaryQuestion  $questionaryQuestion
     * @return \Illuminate\Http\Response
     */
    public function destroy(Questionary $questionary, QuestionaryQuestion $questionaryQuestion)
    {
        $this->authorize('delete', [QuestionaryQuestion::class, $questionary, $questionaryQuestion]);

        $questionaryQuestion->delete();

        return response()->json([
            'data' => $questionaryQuestion,
            'message' => 'Questionary Question deleted successfully.',
            'status' => '1'
        ]);
    }

    public function shift(Questionary $questionary, QuestionaryQuestion $questionaryQuestion, Request $request)
    {
        $this->authorize('update', [Questionary::class, $questionary]);

        $up = $request->boolean('dragUp');
        $next_question = QuestionaryQuestion::findOrFail($request->input("nextIndex"));

        $questionaryQuestion->move(!$up ? 'moveAfter' : 'moveBefore', $next_question);

        return response()->json([
            'data' => $questionaryQuestion,
            'message' => __('strings.company_product_category_updated'),
            'status' => '1'
        ]);
    }

    public function bulkUpdate(Questionary $questionary, BulkQuestionaryQuestionRequest $request)
    {
        $this->authorize('update', [Questionary::class, $questionary]);

        


        return response()->json([
            'data' => $questionaryQuestions,
            'message' => __('strings.company_product_category_updated'),
            'status' => '1'
        ]);
    }
}
