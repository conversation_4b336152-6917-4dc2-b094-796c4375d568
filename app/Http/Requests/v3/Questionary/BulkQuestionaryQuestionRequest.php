<?php

namespace App\Http\Requests\v3\Questionary;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class BulkQuestionaryQuestionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $options_validation = [];

        foreach (config('questionary.type_with_options') as $type => $validation) {
            if ($this->type == $type) {
                $options_validation = $validation['update'];
                break;
            }
        }

        if (!count($options_validation) && $this->has('options')) {
            $this->merge([
                'options' => null,
            ]);
        }

        return [
            'question' => [
                'max:1000',
            ],
            'options' => $options_validation,
            'options.*' => [],
            'required' => [
                'in:0,1',
            ],
            'order' => [
                'sometimes',
                'integer',
                'min:0',
            ],
            'type' => [
                'required',
                'bail',
                Rule::in(config('questionary.type')),
            ],
            'properties' => [
                'sometimes',
            ],
        ];
    }
}
